# CodingBuddy

**An AI-powered interactive coding assistant for Geany IDE**

Coding<PERSON>udd<PERSON> transforms <PERSON>ean<PERSON> into an intelligent development environment with AI-powered code analysis, interactive chat interface, and multi-provider AI support. Think of it as having Cline/Augment directly integrated into your favorite lightweight IDE.

## ✨ Features

### 🤖 Interactive AI Chat Interface
- **Persistent conversations** with full history tracking
- **Context-aware interactions** that remember your coding session
- **Command-based interface** (`/new`, `/history`, `/quit`)
- **Multi-turn conversations** for complex problem solving
- **Cost and token tracking** for usage monitoring

### 🔧 Code Analysis & Generation
- **Real-time code analysis** with AI-powered suggestions
- **Multi-language support** for error detection and improvements
- **Code generation** with context awareness
- **Documentation generation** for functions and classes
- **Refactoring suggestions** with AI recommendations

### 🌐 Multi-Provider AI Support
- **Primary**: OpenRouter.ai (400+ models, single API key)
- **Secondary**: Anthropic Claude, OpenAI GPT, DeepSeek
- **Local**: Ollama integration for offline AI models
- **Automatic fallback** when providers are unavailable
- **Cost optimization** through intelligent model selection
- **Privacy-first** with local model options

### 💾 Smart Persistence
- **Conversation history** automatically saved and restored
- **Cross-session continuity** - conversations survive Geany restarts
- **JSON-based storage** for human-readable conversation files
- **Caching system** for improved performance and reduced API costs

## 🚀 Quick Start

### Prerequisites
- Geany IDE with GeanyLua plugin
- Lua 5.1+ with lua-socket library
- API key for at least one AI provider

### Installation

#### Automated Installation (Recommended)
```bash
git clone https://github.com/skizap/CodingBuddy.git
cd CodingBuddy
./install.sh
```

The automated installer will:
- Detect your Linux distribution
- Install all dependencies automatically
- Set up the plugin and configuration
- Run tests to verify installation

#### Manual Installation
1. **Clone the repository:**
   ```bash
   git clone https://github.com/skizap/CodingBuddy.git
   cd CodingBuddy
   ```

2. **Install to Geany:**
   ```bash
   cp -r codingbuddy ~/.config/geany/plugins/geanylua/
   ```

3. **Configure API keys:**
   ```bash
   cp codingbuddy/config.sample.json ~/.config/geany/plugins/geanylua/codingbuddy/config.json
   # Edit config.json with your API keys
   ```

4. **Restart Geany** and look for CodingBuddy in the Tools menu

> **For detailed installation instructions, see [INSTALL.md](INSTALL.md)**

### First Steps

1. **Open the chat interface:** Tools → CodingBuddy: Open Chat
2. **Start chatting:** Type your coding questions or requests
3. **Analyze code:** Tools → CodingBuddy: Analyze with Chat
4. **Explore commands:** Type `/help` in the chat interface

### 🔒 Security Features

- **Task Mode with Approvals** - Review AI file operations before execution
- **Conversation Encryption** - Optional AES-256 encryption for chat history
- **Sandbox Environment** - All AI file operations restricted to working directory

### ⚡ Task Mode and Approvals

Enable task mode for enhanced safety when AI performs file operations:

```
User: /task start
System: Task mode enabled. AI requests for file operations will require approval.

User: Create a hello world script in Python
AI: I'll create a Python hello world script for you.

Operation 1 (write_file) has been queued for approval. Use /approve 1 to execute or /reject 1 to cancel.

User: /ops
Operations Queue:
[10:30] ID:1 write_file - Write to: hello.py (PENDING)

User: /approve 1
System: Approved operation 1

User: Thanks!
System: Executed approved operations:
- Operation 1 (write_file): Success
```

### 💬 Usage Examples

### Interactive Chat Session
```
=== New Conversation ===
Messages: 0 | Tokens: 0 | Cost: $0.0000

Your message: Can you help me optimize this Python function?

[12:34] You: Can you help me optimize this Python function?
[12:34] Assistant: I'd be happy to help! Please share the function code.

Your message: def find_max(numbers):
    max_val = numbers[0]
    for num in numbers:
        if num > max_val:
            max_val = num
    return max_val

[12:35] Assistant: Here are several optimizations:

1. **Use built-in max()** (most Pythonic):
   ```python
   def find_max(numbers):
       return max(numbers)
   ```

2. **Handle edge cases**:
   ```python
   def find_max(numbers):
       if not numbers:
           raise ValueError("Cannot find max of empty list")
       return max(numbers)
   ```
```

### Code Analysis
- Open any code file in Geany
- Select Tools → CodingBuddy: Analyze with Chat
- Get AI-powered analysis with suggestions for improvements

### Chat Commands
- `/new` - Start a new conversation
- `/history` - Browse previous conversations
- `/quit` - Close chat interface
- `/help` - Show available commands

## 📁 Project Structure

```
CodingBuddy/
├── README.md                    # This file
├── INSTALL.md                   # Detailed installation guide
├── USER_GUIDE.md               # Comprehensive user manual
├── DEVELOPER.md                # API reference and development guide
├── codingbuddy/                # Main plugin directory
│   ├── main.lua                # Plugin entry point
│   ├── ai_connector.lua        # Multi-provider AI interface
│   ├── analyzer.lua            # Code analysis engine
│   ├── chat_interface.lua      # Interactive chat system
│   ├── conversation_manager.lua # Conversation state management
│   ├── config.lua              # Configuration management
│   ├── config.sample.json      # Sample configuration
│   ├── dialogs.lua             # UI components
│   ├── json.lua                # JSON utilities
│   ├── utils.lua               # Utility functions
│   └── prompts/                # AI prompt templates
├── docs/                       # Additional documentation
├── examples/                   # Usage examples
└── tests/                      # Test suite
```

## 🔧 Configuration

CodingBuddy supports multiple AI providers with automatic fallback:

```json
{
  "providers": {
    "openrouter": {
      "api_key": "your-openrouter-key",
      "base_url": "https://openrouter.ai/api/v1",
      "models": ["anthropic/claude-3.5-sonnet", "openai/gpt-4"]
    },
    "anthropic": {
      "api_key": "your-anthropic-key",
      "base_url": "https://api.anthropic.com/v1"
    }
  },
  "default_provider": "openrouter",
  "cache_enabled": true,
  "max_conversation_length": 50
}
```

### 🔒 Encryption Setup (Optional)

Protect your conversation history with AES-256 encryption:

1. **Install OpenSSL** (required for encryption):
   ```bash
   sudo apt install openssl  # Ubuntu/Debian
   brew install openssl      # macOS
   ```

2. **Set encryption passphrase**:
   ```bash
   export CODINGBUDDY_PASSPHRASE="your-very-secure-passphrase-here"
   # Add to ~/.bashrc or ~/.zshrc to persist across sessions
   ```

3. **Enable encryption in config**:
   ```json
   {
     "conversation_encryption": true,
     // ... other settings
   }
   ```

**Security Notes:**
- Use a strong 20+ character passphrase with mixed case, numbers, and symbols
- Never store the passphrase in config files or version control
- If you lose your passphrase, encrypted conversations cannot be recovered
- See [ENCRYPTION.md](ENCRYPTION.md) for detailed security information

## 📚 Documentation

- **[Installation Guide](INSTALL.md)** - Detailed setup instructions
- **[User Guide](USER_GUIDE.md)** - Complete usage manual with examples
- **[Ollama Setup Guide](OLLAMA_SETUP.md)** - Local AI models setup and configuration
- **[Troubleshooting Guide](TROUBLESHOOTING.md)** - Fix common issues
- **[Developer Guide](DEVELOPER.md)** - API reference and development info
- **[Chat Interface](docs/chat_interface.md)** - Interactive chat system details
- **[AI Enhancements](docs/anthropic_enhancements.md)** - Advanced AI features

## 🧪 Testing

Run the test suite to verify installation:

```bash
cd tests
lua test_ai_connector.lua
lua test_chat_interface.lua
```

## 🤝 Contributing

We welcome contributions! Please see [DEVELOPER.md](DEVELOPER.md) for:
- Development setup
- API documentation
- Coding standards
- Contribution guidelines

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- **Geany IDE** - Lightweight and powerful text editor
- **GeanyLua** - Lua scripting support for Geany
- **OpenRouter.ai** - Multi-provider AI access platform
- **Anthropic, OpenAI, DeepSeek** - AI model providers

## 🔗 Links

- [Geany IDE](https://www.geany.org/)
- [GeanyLua Plugin](https://plugins.geany.org/geanylua.html)
- [OpenRouter.ai](https://openrouter.ai/)
- [Project Issues](https://github.com/skizap/CodingBuddy/issues)

---

**Transform your coding experience with AI-powered assistance directly in Geany!**
