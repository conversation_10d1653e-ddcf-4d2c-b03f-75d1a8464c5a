[package]
name = "multiapp-sidecar"
version = "1.0.0"
description = "MultiappV1 Tauri Sidecar"
authors = ["MultiappV1"]
license = "MIT"
repository = ""
default-run = "multiapp-sidecar"
edition = "2021"
rust-version = "1.70"

[build-dependencies]
tauri-build = { version = "2.0", features = [] }

[dependencies]
serde_json = "1.0"
serde = { version = "1.0", features = ["derive"] }
tauri = { version = "2.0", features = ["shell-open"] }
tauri-plugin-shell = "2.0"
tauri-plugin-fs = "2.0"
tauri-plugin-http = "2.0"
tokio = { version = "1.0", features = ["full"] }
uuid = { version = "1.0", features = ["serde", "v4"] }

# Encryption for persistent state
aes-gcm = "0.10"
argon2 = "0.5"
rand = "0.8"
base64 = "0.22"

# Unix domain socket and HTTP server
hyper = { version = "1.0", features = ["full"] }
hyper-util = "0.1"
tower = { version = "0.4", features = ["full"] }
tower-http = { version = "0.5", features = ["cors"] }

# Terminal emulator dependencies
portable-pty = "0.8"

# Error handling and async
anyhow = "1.0"
thiserror = "1.0"
futures = "0.3"
async-trait = "0.1"
chrono = { version = "0.4", features = ["serde"] }

[features]
# by default Tauri runs in production mode
# when `tauri dev` runs it is executed with `cargo run --no-default-features` if `devPath` is an URL
default = ["custom-protocol"]
# this feature is used for production builds where `devPath` points to the filesystem
# DO NOT REMOVE!!
custom-protocol = ["tauri/custom-protocol"]
