{"name": "multiapp-sidecar", "version": "1.0.0", "description": "MultiappV1 Tauri Sidecar - Dark mode UI for GeanyLua CodingBuddy", "main": "src/main.js", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "tauri": "tauri", "tauri:dev": "tauri dev", "tauri:build": "tauri build", "lint": "eslint src/", "format": "prettier --write src/"}, "dependencies": {"@tauri-apps/api": "^2.0.0", "@tauri-apps/plugin-shell": "^2.0.0", "@tauri-apps/plugin-fs": "^2.0.0", "@tauri-apps/plugin-http": "^2.0.0", "lucide-react": "^0.263.1", "react": "^18.2.0", "react-dom": "^18.2.0", "xterm": "^5.3.0", "xterm-addon-fit": "^0.8.0", "xterm-addon-web-links": "^0.9.0"}, "devDependencies": {"@tauri-apps/cli": "^2.0.0", "@types/react": "^18.2.15", "@types/react-dom": "^18.2.7", "@vitejs/plugin-react": "^4.0.3", "autoprefixer": "^10.4.14", "eslint": "^8.45.0", "eslint-plugin-react": "^7.32.2", "eslint-plugin-react-hooks": "^4.6.0", "postcss": "^8.4.27", "prettier": "^3.0.0", "tailwindcss": "^3.3.3", "typescript": "^5.0.2", "vite": "^4.4.4"}}