@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom dark theme utilities */
@layer utilities {
  .glass-dark {
    @apply bg-dark-800/50 backdrop-blur-sm border border-dark-700/50;
  }
  
  .button-primary {
    @apply bg-accent-600 hover:bg-accent-700 text-white font-medium py-2 px-4 rounded-lg transition-colors duration-200;
  }
  
  .button-secondary {
    @apply bg-dark-700 hover:bg-dark-600 text-dark-200 font-medium py-2 px-4 rounded-lg transition-colors duration-200;
  }
  
  .button-danger {
    @apply bg-red-600 hover:bg-red-700 text-white font-medium py-2 px-4 rounded-lg transition-colors duration-200;
  }
  
  .input-dark {
    @apply bg-dark-800 border border-dark-600 text-dark-100 placeholder-dark-400 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-accent-500 focus:border-transparent;
  }
}

/* Terminal specific styles */
.terminal-container {
  @apply bg-dark-900 rounded-lg overflow-hidden;
}

.terminal-container .xterm {
  padding: 8px;
}

.terminal-container .xterm-viewport {
  overflow-y: hidden !important;
}

/* Notification styles */
.notification-enter {
  opacity: 0;
  transform: translateX(100%);
}

.notification-enter-active {
  opacity: 1;
  transform: translateX(0);
  transition: opacity 300ms, transform 300ms;
}

.notification-exit {
  opacity: 1;
}

.notification-exit-active {
  opacity: 0;
  transform: translateX(100%);
  transition: opacity 300ms, transform 300ms;
}

/* Custom scrollbar for dark theme */
.custom-scrollbar {
  scrollbar-width: thin;
  scrollbar-color: #475569 #1e293b;
}

.custom-scrollbar::-webkit-scrollbar {
  width: 6px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: #1e293b;
  border-radius: 3px;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background: #475569;
  border-radius: 3px;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: #64748b;
}
