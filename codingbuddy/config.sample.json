{"provider": "openrouter", "fallback_chain": ["openrouter", "openai", "anthropic", "deepseek", "ollama"], "task_models": {"analysis": "anthropic/claude-3.5-sonnet"}, "provider_models": {"openrouter": "anthropic/claude-3.5-sonnet", "openai": "gpt-4o-mini", "anthropic": "claude-3-5-sonnet-latest", "deepseek": "deepseek-chat", "ollama": "llama3.1"}, "workspace_root": null, "openrouter_api_key": "YOUR_OPENROUTER_API_KEY", "openai_api_key": "YOUR_OPENAI_API_KEY", "anthropic_api_key": "YOUR_ANTHROPIC_API_KEY", "deepseek_api_key": "YOUR_DEEPSEEK_API_KEY", "cache_enabled": true, "log_enabled": true, "timeout": 30, "conversation_encryption": false, "terminal_allow": ["ls", "rg", "grep", "sed", "awk", "python", "lua", "bash", "sh", "node", "npm", "cat", "head", "tail", "wc", "sort", "uniq", "find", "which", "echo", "pwd", "date", "git"], "terminal_deny": ["rm -rf", "shutdown", "reboot", "mkfs", "dd if=", "cryptsetup", "sudo ", "su ", "chmod 777", "chown", "mount", "umount", "fdisk", "parted", "format"], "cost": {"enabled": true, "currency": "USD", "prices_per_1k": {"openai": {"default_input": 0.003, "default_output": 0.006}, "anthropic": {"default_input": 0.003, "default_output": 0.015}, "openrouter": {"default_input": 0.0, "default_output": 0.0}, "deepseek": {"default_input": 0.002, "default_output": 0.002}, "ollama": {"default_input": 0.0, "default_output": 0.0}}}, "sidecar_enabled": false, "sidecar_url": "http://localhost:8765", "sidecar_auto_approve_read_ops": true, "notes": {"provider": "Which provider to prefer initially.", "fallback_chain": "Order of providers to try when requests fail.", "task_models": "Preferred model per task type (analysis/generation/docs/refactor).", "provider_models": "Default model to use for each provider if task model is ambiguous.", "workspace_root": "Root directory for file operations. Defaults to PWD or current working directory. Used for sandboxing.", "*_api_key": "You can set via environment variables instead of here.", "cache_enabled": "Caches analysis results by code hash in cache/.", "log_enabled": "Writes logs to logs/codingbuddy.log.", "timeout": "HTTP timeout in seconds.", "conversation_encryption": "Enable encryption-at-rest for conversation files. Requires CODINGBUDDY_PASSPHRASE environment variable.", "terminal_allow": "List of command prefixes allowed for terminal execution. Commands must start with one of these strings to be permitted.", "terminal_deny": "List of command patterns that are explicitly denied, regardless of allowlist. Deny list takes precedence over allow list.", "cost": "If enabled, estimates cost by provider and logs it."}}